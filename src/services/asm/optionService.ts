import { api } from 'src/boot/axios';
import type { Option } from 'src/types/models';

// Define types for option creation and update
export interface CreateOptionData {
  optionText: string;
  itemBlockId: number;
  value?: number;
  imagePath?: string;
  nextSection?: number;
  [key: string]: unknown; // Add index signature for FormData compatibility
}

export interface UpdateOptionData {
  optionText?: string;
  itemBlockId: number;
  value?: number;
  imagePath?: string;
  nextSection?: number;
  sequence?: number; // Add sequence field for drag-and-drop reordering
  [key: string]: unknown; // Add index signature for FormData compatibility
}

export class OptionService {
  private path = '/options';

  /**
   * Create option using POST /options with itemBlockId in request body
   * POST /options
   */
  async createOption(data: CreateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);

      const response = await api.post<Option>(this.path, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      return response.data;
    } catch (error) {
      console.error('❌ Error creating option:', error);
      console.error('❌ Request data was:', JSON.stringify(data, null, 2));
      throw new Error('Create option failed');
    }
  }

  /**
   * Create option for a specific itemBlock (wrapper method)
   * POST /options
   */
  async createOptionForItemBlock(
    itemBlockId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    // Ensure itemBlockId is included in the request body
    const requestData = {
      ...data,
      itemBlockId: itemBlockId,
    };

    return this.createOption(requestData, file);
  }

  /**
   * Create option for a specific question
   * POST /options/{questionId}
   */
  async createOptionForQuestion(
    questionId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    try {
      // Validate required fields
      if (!questionId) {
        throw new Error('questionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);

      const response = await api.post<Option>(`${this.path}/${questionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      return response.data;
    } catch (error) {
      console.error('❌ Error creating option for question:', error);
      console.error('❌ Request data was:', JSON.stringify({ questionId, data }, null, 2));
      throw new Error('Create option failed');
    }
  }

  createOptionForQuestionFormData(questionId: number, formData: FormData): Promise<Option> {
    return api.post(`/options/${questionId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Update option by optionId
   * PATCH /options/{optionId}
   */
  async updateOption(optionId: number, data: UpdateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);
      const response = await api.patch<Option>(`${this.path}/${optionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      return response.data;
    } catch (error) {
      console.error('Error updating option:', error);
      throw new Error('Update option failed');
    }
  }

  /**
   * Update option for a specific question
   * PATCH /options/{questionId}/{optionId}
   */
  async updateOptionForQuestion(
    questionId: number,
    optionId: number,
    data: UpdateOptionData,
    file?: File,
  ): Promise<Option> {
    try {
      // Validate required fields
      if (!questionId) {
        throw new Error('questionId is required');
      }
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);
      const response = await api.patch<Option>(`${this.path}/${questionId}/${optionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      return response.data;
    } catch (error) {
      console.error('Error updating option for question:', error);
      throw new Error('Update option failed');
    }
  }

  // Keep existing methods for backward compatibility
  async getAllOptions(): Promise<Option[]> {
    try {
      const response = await api.get<Option[]>(this.path);
      return response.data;
    } catch (error) {
      console.error('Error fetching all options:', error);
      throw new Error('Fetch options failed');
    }
  }

  async getOptionById(id: number): Promise<Option> {
    try {
      const response = await api.get<Option>(`${this.path}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching option by ID:', error);
      throw new Error('Fetch option failed');
    }
  }

  // Alias for backward compatibility
  async getOption(id: number): Promise<Option> {
    return this.getOptionById(id);
  }

  async findImagePath(optionText: string): Promise<string> {
    try {
      const response = await api.get<{ imagePath: string }>(`${this.path}/file/${optionText}`);
      return response.data.imagePath;
    } catch (error) {
      console.error('Error fetching imagePath:', error);
      throw new Error('Fetch imagePath failed');
    }
  }

  async uploadOptionWithFile(option: Option, file?: File): Promise<Option> {
    const formData = new FormData();

    formData.append('optionText', option.optionText ?? '');
    formData.append('value', option.value?.toString() ?? '0');
    formData.append('sequence', option.sequence?.toString() ?? '1');
    formData.append('itemBlockId', option.itemBlockId?.toString() ?? '0');

    if (file) {
      formData.append('imagePath', file); // แนบไฟล์
      console.log(file, 'eiei');
    }

    const response = await api.post<Option>(this.path, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });

    return response.data;
  }

  async removeOption(id: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`);
    } catch (error) {
      console.error('Error removing option:', error);
      throw new Error('Remove option failed');
    }
  }

  async removeFileName(optionText: string): Promise<void> {
    try {
      await api.delete(`${this.path}/deleteFile/${optionText}`);
    } catch {
      throw new Error('Remove response failed');
    }
  }

  /**
   * Helper method to convert data to FormData for multipart/form-data requests
   */
  private toFormData(data: Record<string, unknown>, file?: File): FormData {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object' && value !== null && !(value instanceof File)) {
          formData.append(key, JSON.stringify(value));
        } else if (
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'boolean'
        ) {
          formData.append(key, String(value));
        } else {
          formData.append(key, JSON.stringify(value));
        }
      }
    });

    if (file) {
      formData.append('file', file);
    }

    return formData;
  }

  /**
   * Validate option data before sending to API
   */
  private validateOptionData(data: CreateOptionData | UpdateOptionData): void {
    if (!data.itemBlockId) {
      throw new Error('itemBlockId is required');
    }

    if (
      'optionText' in data &&
      data.optionText !== undefined &&
      typeof data.optionText !== 'string'
    ) {
      throw new Error('optionText must be a string');
    }

    if (data.value !== undefined && typeof data.value !== 'number') {
      throw new Error('value must be a number');
    }
  }

  /**
   * Create option with validation
   */
  async createOptionWithValidation(
    itemBlockId: number,
    data: CreateOptionData,
    file?: File,
  ): Promise<Option> {
    this.validateOptionData(data);
    return this.createOptionForItemBlock(itemBlockId, data, file);
  }

  /**
   * Update option with validation
   */
  async updateOptionWithValidation(
    optionId: number,
    data: UpdateOptionData,
    file?: File,
  ): Promise<Option> {
    this.validateOptionData(data);
    return this.updateOption(optionId, data, file);
  }

  /**
   * Update option without notifications (for auto-save)
   * PATCH /options/{optionId}
   */
  async updateOptionSilent(optionId: number, data: UpdateOptionData, file?: File): Promise<Option> {
    try {
      // Validate required fields
      if (!optionId) {
        throw new Error('optionId is required');
      }
      if (!data.itemBlockId) {
        throw new Error('itemBlockId is required in request body');
      }

      const formData = this.toFormData(data, file);
      const response = await api.patch<Option>(`${this.path}/${optionId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      // No notification for silent updates (auto-save)
      return response.data;
    } catch (error) {
      console.error('Error updating option (silent):', error);
      throw new Error('Update option failed');
    }
  }

  /**
   * Update multiple option sequences after drag-and-drop reordering
   * This method updates each option's sequence individually
   */
  async updateOptionSequences(
    options: Array<{ id: number; sequence: number; optionText: string; value: number }>,
    itemBlockId: number,
  ): Promise<Option[]> {
    try {
      const updatePromises = options.map(async (option) => {
        if (!option.id) {
          // Option without ID, skip
          return null;
        }

        const updateData: UpdateOptionData = {
          itemBlockId,
          optionText: option.optionText,
          value: option.value,
          sequence: option.sequence,
        };

        return await this.updateOptionSilent(option.id, updateData);
      });

      const results = await Promise.all(updatePromises);
      const updatedOptions = results.filter((option): option is Option => option !== null);

      return updatedOptions;
    } catch (error) {
      console.error('❌ [OPTION-SERVICE] Failed to update option sequences:', error);
      throw new Error('Update option sequences failed');
    }
  }
}
