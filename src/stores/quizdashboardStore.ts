// stores/quizDashboard.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type {
  AssessmentMeta,
  ChartData,
  QuestionResponseData,
  ParticipantData,
  ParticipantDetails,
} from 'src/types/quiz-dashboard';
import { quizDashboardService } from 'src/services/quiz/quiz-dashboardService';
import type { DataParams, DataResponse } from 'src/types/data';

export const useQuizDashboardStore = defineStore('quizDashboard', () => {
  // State
  const assessmentMeta = ref<AssessmentMeta | null>(null);
  const scoreDistribution = ref<ChartData | null>(null);
  const questionResponses = ref<DataResponse<QuestionResponseData> | null>(null);
  const participants = ref<DataResponse<ParticipantData> | null>(null);
  const participantDetails = ref<DataResponse<ParticipantDetails> | null>(null);

  // Loading states
  const isLoadingMeta = ref(false);
  const isLoadingScoreDistribution = ref(false);
  const isLoadingQuestions = ref(false);
  const isLoadingParticipants = ref(false);
  const isLoadingParticipantDetails = ref(false);

  // Error state
  const error = ref<string | null>(null);

  // Getters (computed)
  const hasError = computed(() => error.value !== null);
  const isAnyLoading = computed(
    () =>
      isLoadingMeta.value ||
      isLoadingScoreDistribution.value ||
      isLoadingQuestions.value ||
      isLoadingParticipants.value ||
      isLoadingParticipantDetails.value,
  );

  // Actions
  const fetchDashboardMeta = async (assessmentId: number) => {
    isLoadingMeta.value = true;
    error.value = null;
    try {
      const data = await quizDashboardService.getDashboardMeta(assessmentId);
      assessmentMeta.value = data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch dashboard meta';
    } finally {
      isLoadingMeta.value = false;
    }
  };

  const fetchScoreDistribution = async (assessmentId: number) => {
    isLoadingScoreDistribution.value = true;
    error.value = null;
    try {
      const data = await quizDashboardService.getScoreDistribution(assessmentId);
      scoreDistribution.value = data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch score distribution';
    } finally {
      isLoadingScoreDistribution.value = false;
    }
  };

  const fetchQuestionResponses = async (assessmentId: number, params: DataParams) => {
    isLoadingQuestions.value = true;
    error.value = null;
    try {
      const data = await quizDashboardService.getQuestionResponses(assessmentId, params);
      questionResponses.value = data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch question responses';
    } finally {
      isLoadingQuestions.value = false;
    }
  };

  const fetchParticipants = async (assessmentId: number, params: DataParams) => {
    isLoadingParticipants.value = true;
    error.value = null;
    try {
      const data = await quizDashboardService.getParticipants(assessmentId, params);
      participants.value = data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch participants';
    } finally {
      isLoadingParticipants.value = false;
    }
  };

  const fetchParticipantDetails = async (participantId: number, params?: Partial<DataParams>) => {
    isLoadingParticipantDetails.value = true;
    error.value = null;
    try {
      const data = await quizDashboardService.getParticipantDetails(participantId, params);
      participantDetails.value = data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch participant details';
    } finally {
      isLoadingParticipantDetails.value = false;
    }
  };

  const fetchParticipantTextFieldGrading = async (
    participantId: number,
    params?: Partial<DataParams>,
  ) => {
    isLoadingParticipantDetails.value = true;
    error.value = null;
    try {
      const data = await quizDashboardService.getParticipantTextFieldGrading(participantId, params);
      participantDetails.value = data;
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : 'Failed to fetch participant textfield grading';
    } finally {
      isLoadingParticipantDetails.value = false;
    }
  };

  const saveCustomScore = async (submissionId: number, questionId: number, score: number) => {
    try {
      const result = await quizDashboardService.saveCustomScore(submissionId, questionId, score);
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to save custom score';
      throw err;
    }
  };

  const clearError = () => {
    error.value = null;
  };

  const reset = () => {
    assessmentMeta.value = null;
    scoreDistribution.value = null;
    questionResponses.value = null;
    participants.value = null;
    participantDetails.value = null;
    isLoadingMeta.value = false;
    isLoadingScoreDistribution.value = false;
    isLoadingQuestions.value = false;
    isLoadingParticipants.value = false;
    isLoadingParticipantDetails.value = false;
    error.value = null;
  };

  return {
    // State
    assessmentMeta,
    scoreDistribution,
    questionResponses,
    participants,
    participantDetails,

    // Loading states
    isLoadingMeta,
    isLoadingScoreDistribution,
    isLoadingQuestions,
    isLoadingParticipants,
    isLoadingParticipantDetails,

    // Error state
    error,

    // Getters
    hasError,
    isAnyLoading,

    // Actions
    fetchDashboardMeta,
    fetchScoreDistribution,
    fetchQuestionResponses,
    fetchParticipants,
    fetchParticipantDetails,
    fetchParticipantTextFieldGrading,
    saveCustomScore,
    clearError,
    reset,
  };
});

// Usage in component:
// <script setup lang="ts">
// import { useQuizDashboardStore } from 'stores/quizDashboard'
//
// const store = useQuizDashboardStore()
//
// // Use reactive state
// console.log(store.assessmentMeta)
// console.log(store.isLoadingMeta)
//
// // Call actions
// await store.fetchDashboardMeta(123)
// </script>
